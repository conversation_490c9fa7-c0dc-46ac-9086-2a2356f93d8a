import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { useLanguage } from '@/context/LanguageContext';
import { QuizService } from '@/services/quizService';
import Layout from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Clock, 
  FileText, 
  Target, 
  Trophy, 
  BookOpen, 
  ArrowLeft, 
  Play, 
  AlertCircle,
  CheckCircle,
  Info,
  Users
} from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';

/**
 * AssessmentDetailScreen Component
 * Shows detailed information about an assessment before starting
 */
const AssessmentDetailScreen: React.FC = () => {
  const { quizId } = useParams<{ quizId: string }>();
  const navigate = useNavigate();
  const { t } = useLanguage();

  // Fetch quiz details
  const {
    data: quiz,
    isLoading,
    error
  } = useQuery({
    queryKey: ['quiz-detail', quizId],
    queryFn: async () => {
      if (!quizId) throw new Error('Quiz ID is required');
      const { data, error } = await QuizService.getQuizById(quizId);
      if (error) throw new Error(error);
      return data;
    },
    enabled: !!quizId
  });

  const getAssessmentTypeIcon = (assessmentType: string) => {
    switch (assessmentType) {
      case 'test_quiz': return <Target className="h-6 w-6" />;
      case 'toeic_test': return <Trophy className="h-6 w-6" />;
      case 'ielts_test': return <BookOpen className="h-6 w-6" />;
      default: return <FileText className="h-6 w-6" />;
    }
  };

  const getAssessmentTypeColor = (assessmentType: string) => {
    switch (assessmentType) {
      case 'test_quiz': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'toeic_test': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'ielts_test': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const handleStartAssessment = () => {
    if (quiz) {
      navigate(`/quiz/${quiz.id}?mode=test`);
    }
  };

  const handleGoBack = () => {
    navigate('/assessment');
  };

  if (error) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-red-600 mb-4">
              {t('assessmentDetail.errorTitle', 'Error Loading Assessment')}
            </h2>
            <p className="text-gray-600 mb-4">
              {t('assessmentDetail.errorMessage', 'Unable to load assessment details. Please try again.')}
            </p>
            <Button onClick={handleGoBack} variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t('assessmentDetail.backToAssessments', 'Back to Assessments')}
            </Button>
          </div>
        </div>
      </Layout>
    );
  }

  if (isLoading) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <Skeleton className="h-8 w-32 mb-6" />
            <Card>
              <CardHeader>
                <Skeleton className="h-8 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2 mb-4" />
                <div className="flex gap-2">
                  <Skeleton className="h-6 w-20" />
                  <Skeleton className="h-6 w-16" />
                </div>
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-2/3 mb-6" />
                <Skeleton className="h-32 w-full" />
              </CardContent>
            </Card>
          </div>
        </div>
      </Layout>
    );
  }

  if (!quiz) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              {t('assessmentDetail.notFound', 'Assessment Not Found')}
            </h2>
            <p className="text-gray-600 mb-4">
              {t('assessmentDetail.notFoundMessage', 'The assessment you are looking for does not exist.')}
            </p>
            <Button onClick={handleGoBack} variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t('assessmentDetail.backToAssessments', 'Back to Assessments')}
            </Button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Back Button */}
          <Button 
            onClick={handleGoBack} 
            variant="ghost" 
            className="mb-6"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('assessmentDetail.backToAssessments', 'Back to Assessments')}
          </Button>

          {/* Assessment Header */}
          <Card className="mb-8">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-2xl mb-3">{quiz.title}</CardTitle>
                  <div className="flex items-center gap-3 mb-4">
                    <Badge variant="secondary" className={getAssessmentTypeColor(quiz.quiz_type?.key || '')}>
                      {getAssessmentTypeIcon(quiz.quiz_type?.key || '')}
                      <span className="ml-2">{quiz.quiz_type?.name}</span>
                    </Badge>
                    <Badge variant="outline" className="text-sm">
                      {quiz.level?.key} {t('common.level', 'Level')}
                    </Badge>
                  </div>
                  <CardDescription className="text-base">
                    {quiz.description}
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              {/* Assessment Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <FileText className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900 dark:text-white">
                    {quiz.total_questions}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {t('assessmentDetail.questions', 'Questions')}
                  </div>
                </div>
                
                <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <Clock className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900 dark:text-white">
                    {quiz.time_limit_minutes}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {t('assessmentDetail.minutes', 'Minutes')}
                  </div>
                </div>
                
                <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <Target className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900 dark:text-white">
                    {quiz.difficulty_level}/5
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {t('assessmentDetail.difficulty', 'Difficulty')}
                  </div>
                </div>
                
                <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <Users className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900 dark:text-white">
                    {quiz.category?.name}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {t('assessmentDetail.category', 'Category')}
                  </div>
                </div>
              </div>

              {/* Start Assessment Button */}
              <div className="text-center">
                <Button 
                  onClick={handleStartAssessment}
                  size="lg"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3"
                >
                  <Play className="h-5 w-5 mr-2" />
                  {t('assessmentDetail.startAssessment', 'Start Assessment')}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Assessment Instructions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Info className="h-5 w-5 mr-2" />
                {t('assessmentDetail.instructions', 'Assessment Instructions')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {quiz.instructions ? (
                  <p className="text-gray-700 dark:text-gray-300">{quiz.instructions}</p>
                ) : (
                  <p className="text-gray-700 dark:text-gray-300">
                    {t('assessmentDetail.defaultInstructions', 'Complete this timed assessment to evaluate your skills. Read each question carefully and select the best answer.')}
                  </p>
                )}
                
                <Separator />
                
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                      <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
                      {t('assessmentDetail.whatToExpect', 'What to Expect')}
                    </h4>
                    <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                      <li>• {t('assessmentDetail.timedAssessment', 'Timed assessment with strict time limits')}</li>
                      <li>• {t('assessmentDetail.forwardOnly', 'Forward-only navigation (cannot go back)')}</li>
                      <li>• {t('assessmentDetail.noHints', 'No hints or explanations during the test')}</li>
                      <li>• {t('assessmentDetail.finalScore', 'Final score and results at the end')}</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-2 text-orange-600" />
                      {t('assessmentDetail.importantNotes', 'Important Notes')}
                    </h4>
                    <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                      <li>• {t('assessmentDetail.stableConnection', 'Ensure stable internet connection')}</li>
                      <li>• {t('assessmentDetail.quietEnvironment', 'Find a quiet environment')}</li>
                      <li>• {t('assessmentDetail.noInterruptions', 'Avoid interruptions during the test')}</li>
                      <li>• {t('assessmentDetail.oneAttempt', 'You have one attempt per assessment')}</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
};

export default AssessmentDetailScreen;
